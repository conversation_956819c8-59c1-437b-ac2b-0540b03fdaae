package com.knet.goods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.annotation.DistributedLock;
import com.knet.common.context.UserContext;
import com.knet.common.enums.KnetCurrencyCode;
import com.knet.common.enums.ProductMark;
import com.knet.common.enums.ProductStatus;
import com.knet.common.exception.ServiceException;
import com.knet.common.service.PricingStrategyService;
import com.knet.common.utils.PriceFormatUtil;
import com.knet.common.utils.RandomStrUtil;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.goods.mapper.KnetProductMapper;
import com.knet.goods.model.dto.req.*;
import com.knet.goods.model.dto.resp.*;
import com.knet.goods.model.dto.third.req.KnetGroupGetSkuSizePlatformPriceReq;
import com.knet.goods.model.dto.third.resp.KnetProductMarketDataVo;
import com.knet.goods.model.dto.third.resp.SubOrderItemResp;
import com.knet.goods.model.entity.KnetProduct;
import com.knet.goods.model.entity.SysUpdateSysSkuEvents;
import com.knet.goods.service.IKnetProductService;
import com.knet.goods.service.ISkuCacheService;
import com.knet.goods.service.ISysSkuService;
import com.knet.goods.service.IThirdApiService;
import com.knet.goods.system.handler.TempTableManager;
import com.knet.goods.system.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import static com.knet.common.constants.UserServicesConstants.KNET_SYS_SKU_CACHE_KEY_PREFIX;
import static com.knet.goods.model.entity.KnetProduct.initKnetProduct;


/**
 * <AUTHOR>
 * @date 2025/2/19 16:18
 * @description: Product service 实现类
 */
@Slf4j
@Service
public class KnetProductServiceImpl extends ServiceImpl<KnetProductMapper, KnetProduct> implements IKnetProductService {
    @Resource
    private KnetProductMapper baseMapper;
    @Resource
    private IThirdApiService thirdApiService;
    @Resource
    private RandomStrUtil randomStrUtil;
    @Resource
    private ISysSkuService sysSkuService;
    @Resource
    private JwtUtil jwtUtil;
    @Resource
    private ISkuCacheService skuCacheService;
    @Resource
    private TempTableManager tempTableManager;
    @Resource
    @Qualifier("goodsThreadPoolExecutor")
    private ThreadPoolExecutor goodsThreadPoolExecutor;
    @Resource
    private PricingStrategyService pricingStrategyService;

    @Override
    public IPage<ProductDtoResp> listProducts(ProductQueryRequest request) {
        QueryWrapper<KnetProduct> queryWrapper = new QueryWrapper<>();
        Page<KnetProduct> page = new Page<>(request.getQueryStartPage(), request.getPageSize());
        page = baseMapper.selectPage(page, queryWrapper);
        IPage<KnetProduct> productPage = baseMapper.selectPage(page, queryWrapper);
        return productPage.convert(this::mapToProductDtoRespWithPricing);
    }

    @Override
    public IPage<ProductBySkuDtoResp> queryProductGroupBySku(ProductQueryRequest request) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        if (ProductMark.ALL.equals(request.getMark())) {
            request.setMark(null);
        }
        // 价格区间转换：将前端传来的策略价格转换为原始价格用于数据库查询
        convertStrategyPriceToOriginalPrice(request);
        IPage<ProductBySkuDtoResp> result = new Page<>(request.getQueryStartPage(), request.getPageSize());
        String accountFromToken = jwtUtil.getAccountFromToken(UserContext.getContext());
        request.setAccount(accountFromToken);
        //获取sku缓存
        Set<String> skus = skuCacheService.matchSkus(request.getSku());
        Set<String> remarks = skuCacheService.matchProductsByRemark(request.getSku());
        // 判断是否需要使用临时表优化
        boolean useTempTable = tempTableManager.shouldUseTempTable(skus.size(), remarks.size());
        Integer count;
        List<ProductBySkuDtoResp> products;
        if (useTempTable) {
            log.info("使用临时表优化查询，SKU 数量: {}, 商品名称 数量: {}", skus.size(), remarks.size());
            log.debug(tempTableManager.getConfigInfo());
            try {
                // 创建并填充临时表
                if (tempTableManager.createAndPopulateTempTable(skus, remarks)) {
                    log.info("使用分表策略执行优化查询");
                    count = baseMapper.queryProductGroupBySkuCountWithSeparateTables(request);
                    products = baseMapper.queryProductGroupBySkuWithSeparateTables(request);
                } else {
                    log.warn("临时表创建失败，回退到传统查询方式");
                    request.setSkus(new ArrayList<>(skus));
                    request.setRemarks(new ArrayList<>(remarks));
                    count = baseMapper.queryProductGroupBySkuCount(request);
                    products = baseMapper.queryProductGroupBySku(request);
                }
            } finally {
                tempTableManager.cleanupTempTable();
            }
        } else {
            log.info("使用传统查询方式，SKU数量: {}, 备注数量: {}", skus.size(), remarks.size());
            request.setSkus(new ArrayList<>(skus));
            request.setRemarks(new ArrayList<>(remarks));
            count = baseMapper.queryProductGroupBySkuCount(request);
            products = baseMapper.queryProductGroupBySku(request);
        }
        if (CollUtil.isNotEmpty(products)) {
            // 批量查询SKU备注信息，提高性能
            List<String> skuList = products.stream()
                    .map(ProductBySkuDtoResp::getSku)
                    .filter(StrUtil::isNotBlank)
                    .distinct()
                    .toList();
            if (CollUtil.isNotEmpty(skuList)) {
                Map<String, Map<String, String>> batchSkuRemarksMap = sysSkuService.getBatchSkuRemarksMap(skuList);
                products.forEach(product -> {
                    Map<String, String> skuRemarksMap = batchSkuRemarksMap.get(product.getSku());
                    if (MapUtil.isNotEmpty(skuRemarksMap)) {
                        product.setImg(skuRemarksMap.getOrDefault("imgUrl", ""));
                        product.setRemarks(skuRemarksMap.getOrDefault("remarks", ""));
                    }
                });
            }
        }
        // 应用价格策略到商品列表
        result.setRecords(applyPricingStrategyToProductList(products));
        result.setTotal(count);
        result.setCurrent(request.getPageNo());
        stopWatch.stop();
        log.info("查询商品列表耗时: {} ms, 使用临时表: {}", stopWatch.getTotalTimeMillis(), useTempTable);
        return result;
    }

    @Override
    public List<ProductSkuSpecPriceDtoResp> queryProductDetails(ProductDetailsQueryRequest request) {
        if (StrUtil.isBlank(request.getAccount())) {
            String accountFromToken = jwtUtil.getAccountFromToken(UserContext.getContext());
            request.setAccount(accountFromToken);
        }
        List<ProductSkuSpecPriceDtoResp> dtoRests = baseMapper.queryProductDetails(request);
        if (CollUtil.isEmpty(dtoRests)) {
            return Collections.emptyList();
        }
        var skuInfoFuture = CompletableFuture.supplyAsync(
                () -> sysSkuService.getSkuRemarksMap(request.getSku()),
                goodsThreadPoolExecutor
        );
        var priceInfosFuture = CompletableFuture.supplyAsync(
                () -> baseMapper.queryProductDetailsPriceInfo(request),
                goodsThreadPoolExecutor
        );
        List<String> sizes = dtoRests.stream().map(ProductSkuSpecPriceDtoResp::getSpec).toList();
        CompletableFuture<List<KnetProductMarketDataVo>> marketDataFuture = CompletableFuture.supplyAsync(
                () -> {
                    try {
                        return thirdApiService.getProductMarketData(new KnetGroupGetSkuSizePlatformPriceReq(request.getSku(), sizes));
                    } catch (Exception e) {
                        log.error("调用kg获取商品市场数据失败, sku: {}, sizes: {}, 异常信息: {}", request.getSku(), sizes, e.getMessage(), e);
                        return Collections.emptyList();
                    }
                },
                goodsThreadPoolExecutor
        );
        try {
            Map<String, String> skuInfoMap = skuInfoFuture.get();
            if (MapUtil.isNotEmpty(skuInfoMap)) {
                dtoRests.forEach(dto -> {
                    dto.setImg(skuInfoMap.getOrDefault("imgUrl", ""));
                    dto.setRemarks(skuInfoMap.getOrDefault("remarks", ""));
                });
            }
            List<SpecPriceDto> priceInfos = priceInfosFuture.get();
            if (CollUtil.isNotEmpty(priceInfos)) {
                // 将价格信息按spec分组
                Map<String, List<SpecPriceDto>> priceInfoMap = priceInfos.stream()
                        .collect(Collectors.groupingBy(SpecPriceDto::getSpec));
                dtoRests.forEach(dtoRest -> {
                    List<SpecPriceDto> priceInfo = priceInfoMap.get(dtoRest.getSpec());
                    if (CollUtil.isNotEmpty(priceInfo)) {
                        dtoRest.setPriceInfo(priceInfo);
                    }
                });
            }
            List<KnetProductMarketDataVo> productMarketData = marketDataFuture.get();
            List<ProductSkuSpecPriceDtoResp> dtoResps = fillPlatformPrice(dtoRests, productMarketData);
            dtoResps = sortBySpec(dtoResps);
            // 应用价格策略
            return applyPricingStrategyToProductDetails(dtoResps);
        } catch (Exception e) {
            log.error("异步查询商品详情信息失败, sku: {}, 异常信息: {}", request.getSku(), e.getMessage(), e);
            throw new ServiceException("查询商品详情失败");
        }
    }

    /**
     * 填充平台价格
     *
     * @param dtoRests          商品详情
     * @param productMarketData 商品市场数据
     * @return 商品详情
     */
    List<ProductSkuSpecPriceDtoResp> fillPlatformPrice(List<ProductSkuSpecPriceDtoResp> dtoRests, List<KnetProductMarketDataVo> productMarketData) {
        if (CollUtil.isEmpty(dtoRests) || CollUtil.isEmpty(productMarketData)) {
            return dtoRests;
        }
        // 将市场数据转换为Map以提高查询效率
        Map<String, KnetProductMarketDataVo> marketDataMap = productMarketData.stream()
                .collect(Collectors.toMap(
                        KnetProductMarketDataVo::getSize,
                        data -> data,
                        (existing, replacement) -> existing,
                        () -> new HashMap<>(productMarketData.size())
                ));
        dtoRests.forEach(dtoRest -> {
            KnetProductMarketDataVo marketData = marketDataMap.get(dtoRest.getSpec());
            if (marketData == null) {
                return;
            }
            Optional.ofNullable(marketData.getStockX())
                    .map(KnetProductMarketDataVo.PlatformProductMarketDataVo::getLowestAskPrice)
                    .ifPresent(price -> dtoRest.setStockPrice(price.getAmount()));
            Optional.ofNullable(marketData.getGoat())
                    .map(KnetProductMarketDataVo.PlatformProductMarketDataVo::getLowestAskPrice)
                    .ifPresent(price -> dtoRest.setGoatPrice(price.getAmount()));
        });
        return dtoRests;
    }

    /**
     * 排序商品尺码
     *
     * @param originate 商品详情
     * @return 排序后的商品详情
     */
    List<ProductSkuSpecPriceDtoResp> sortBySpec(List<ProductSkuSpecPriceDtoResp> originate) {
        if (CollUtil.isEmpty(originate)) {
            return originate;
        }
        return originate.stream()
                .filter(item -> item.getSpec() != null)
                .sorted(Comparator.comparing(item -> {
                    String spec = item.getSpec();
                    try {
                        return Double.parseDouble(spec);
                    } catch (NumberFormatException e) {
                        // 非数字情况，使用字符串本身，但确保排序在数字之后
                        return Double.MAX_VALUE;
                    }
                }, Comparator.nullsLast(Double::compareTo)))
                .toList();
    }

    /**
     * 应用价格策略到商品详情
     *
     * @param productDetails 商品详情列表
     * @return 应用策略后的商品详情列表
     */
    private List<ProductSkuSpecPriceDtoResp> applyPricingStrategyToProductDetails(List<ProductSkuSpecPriceDtoResp> productDetails) {
        if (CollUtil.isEmpty(productDetails)) {
            return productDetails;
        }
        productDetails.forEach(detail -> {
            // 保存原始价格
            detail.setOriginalMinPrice(detail.getMinPrice());
            detail.setOriginalMaxPrice(detail.getMaxPrice());
            detail.setOriginalAvgPrice(detail.getAvgPrice());
            detail.setOriginalTotalPrice(detail.getTotalPrice());
            // 应用价格策略到最低价和最高价
            // 注意：数据库返回的价格字段是美分值的字符串，需要直接转换为Long
            if (StrUtil.isNotBlank(detail.getMinPrice())) {
                Long originalMinPriceCents = Long.parseLong(detail.getMinPrice());
                Long strategyMinPriceCents = pricingStrategyService.applyPricingStrategy(originalMinPriceCents);
                detail.setMinPrice(String.valueOf(strategyMinPriceCents));
            }
            if (StrUtil.isNotBlank(detail.getMaxPrice())) {
                Long originalMaxPriceCents = Long.parseLong(detail.getMaxPrice());
                Long strategyMaxPriceCents = pricingStrategyService.applyPricingStrategy(originalMaxPriceCents);
                detail.setMaxPrice(String.valueOf(strategyMaxPriceCents));
            }
            // 从priceInfo中计算策略价格后的总价和平均价
            if (CollUtil.isNotEmpty(detail.getPriceInfo())) {
                // 先应用价格策略到价格信息列表
                detail.getPriceInfo().forEach(priceInfo -> {
                    // 保存原始价格
                    priceInfo.setOriginalPrice(priceInfo.getPrice());
                    // 应用价格策略
                    // 注意：priceInfo.getPrice()来自数据库的price字段，是美分值的字符串
                    if (StrUtil.isNotBlank(priceInfo.getPrice())) {
                        Long originalPriceCents = Long.parseLong(priceInfo.getPrice());
                        Long strategyPriceCents = pricingStrategyService.applyPricingStrategy(originalPriceCents);
                        priceInfo.setPrice(String.valueOf(strategyPriceCents));
                    }
                });
                // 基于策略价格计算总价和平均价
                calculateStrategyPricesFromPriceInfo(detail);
            }
        });
        return productDetails;
    }

    /**
     * 基于策略价格计算总价和平均价
     *
     * @param detail 商品详情
     */
    private void calculateStrategyPricesFromPriceInfo(ProductSkuSpecPriceDtoResp detail) {
        if (CollUtil.isEmpty(detail.getPriceInfo())) {
            return;
        }
        // 统计策略价格后的总价和数量
        long totalStrategyPriceCents = 0L;
        int totalQuantity = 0;
        for (SpecPriceDto priceInfo : detail.getPriceInfo()) {
            if (StrUtil.isNotBlank(priceInfo.getPrice()) && priceInfo.getQty() != null && priceInfo.getQty() > 0) {
                Long strategyPriceCents = Long.parseLong(priceInfo.getPrice());
                int quantity = priceInfo.getQty();
                // 累加总价（策略价格 * 数量）
                totalStrategyPriceCents += strategyPriceCents * quantity;
                totalQuantity += quantity;
            }
        }
        // 设置策略价格后的总价
        detail.setTotalPrice(String.valueOf(totalStrategyPriceCents));
        // 计算策略价格后的平均价（保留小数点后2位）
        if (totalQuantity > 0) {
            double avgStrategyPrice = (double) totalStrategyPriceCents / totalQuantity;
            // 转换为美分值，保留2位小数精度
            long avgStrategyPriceCents = Math.round(avgStrategyPrice);
            detail.setAvgPrice(String.valueOf(avgStrategyPriceCents));
        }
        log.debug("计算策略价格统计: spec={}, 总价={}美分, 平均价={}美分, 总数量={}",
                detail.getSpec(), totalStrategyPriceCents,
                totalQuantity > 0 ? Math.round((double) totalStrategyPriceCents / totalQuantity) : 0,
                totalQuantity);
    }

    /**
     * 应用价格策略到商品列表
     *
     * @param productList 商品列表
     * @return 应用策略后的商品列表
     */
    private List<ProductBySkuDtoResp> applyPricingStrategyToProductList(List<ProductBySkuDtoResp> productList) {
        if (CollUtil.isEmpty(productList)) {
            return productList;
        }
        productList.forEach(product -> {
            // 保存原始价格（数据库返回的是美分值的字符串）
            product.setOriginalMinPrice(product.getMinPrice());
            product.setOriginalMaxPrice(product.getMaxPrice());
            // 应用价格策略到最低价
            // 注意：数据库返回的minPrice/maxPrice是美分值的字符串，需要直接转换为Long
            if (StrUtil.isNotBlank(product.getMinPrice())) {
                Long originalMinPriceCents = Long.parseLong(product.getMinPrice());
                Long strategyMinPriceCents = pricingStrategyService.applyPricingStrategy(originalMinPriceCents);
                product.setMinPrice(String.valueOf(strategyMinPriceCents));
            }
            // 应用价格策略到最高价
            if (StrUtil.isNotBlank(product.getMaxPrice())) {
                Long originalMaxPriceCents = Long.parseLong(product.getMaxPrice());
                Long strategyMaxPriceCents = pricingStrategyService.applyPricingStrategy(originalMaxPriceCents);
                product.setMaxPrice(String.valueOf(strategyMaxPriceCents));
            }
        });
        return productList;
    }

    /**
     * 将KnetProduct转换为ProductDtoResp并应用价格策略
     *
     * @param knetProduct 商品实体
     * @return 应用价格策略后的商品响应DTO
     */
    private ProductDtoResp mapToProductDtoRespWithPricing(KnetProduct knetProduct) {
        // 先使用原有方法转换
        ProductDtoResp productDto = knetProduct.mapToProductDtoResp();
        // 保存原始价格
        productDto.setOriginalPrice(productDto.getPrice());
        // 应用价格策略
        if (StrUtil.isNotBlank(productDto.getPrice())) {
            Long originalPriceCents = PriceFormatUtil.formatYuanToCents(productDto.getPrice());
            Long strategyPriceCents = pricingStrategyService.applyPricingStrategy(originalPriceCents);
            productDto.setPrice(PriceFormatUtil.formatCentsToYuan(strategyPriceCents));
        }
        return productDto;
    }

    @Override
    public KnetProduct createByKnet(CreateKnetProductRequest.ProductDto request) {
        if (!KnetCurrencyCode.USD.equals(request.getCurrency())) {
            throw new ServiceException("暂时只支持美元");
        }
        KnetProduct knetProduct = initKnetProduct(request);
        knetProduct.setListingId(randomStrUtil.getProductId());
        if (KnetCurrencyCode.USD.equals(request.getCurrency())) {
            knetProduct.setPrice(request.getPrice());
        }
        log.info("🆕 商品创建 | listingId: {} | sku: {} | spec: {} | 价格: {} | 状态: {}",
                knetProduct.getListingId(), knetProduct.getSku(),
                knetProduct.getSpec(), knetProduct.getPrice(), knetProduct.getStatus());
        return knetProduct;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void insertIgnoreBatch(List<KnetProduct> list) {
        if (CollUtil.isEmpty(list)) {
            log.warn("⚠️ 批量插入商品 | 商品列表为空 | ");
            return;
        }
        log.info("🔄 批量插入商品 || 商品数量: {}", list.size());
        try {
            baseMapper.insertIgnoreBatch(list);
            log.info("✅ 批量插入商品完成 | 成功插入商品数: {} | 操作时间: {}", list.size(), new Date());
        } catch (Exception e) {
            log.error("❌ 批量插入商品失败 | 商品数量: {} | 错误信息: {}", list.size(), e.getMessage(), e);
            throw new ServiceException("批量插入商品失败");
        }
    }

    @Override
    public List<String> queryByListingIds(List<String> listingIds) {
        LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(KnetProduct::getListingId, listingIds);
        List<KnetProduct> knetProducts = baseMapper.selectList(queryWrapper);
        if (CollUtil.isNotEmpty(knetProducts)) {
            return knetProducts.stream()
                    .map(KnetProduct::getOneId)
                    .toList();
        }
        return Collections.emptyList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> updateKnetProductForOffSale(List<OffSaleKnetProductRequest.ProductDto> products) {
        List<String> listingIds = products.stream()
                .map(OffSaleKnetProductRequest.ProductDto::getListingId)
                .distinct()
                .toList();
        if (CollUtil.isEmpty(listingIds)) {
            return Collections.emptyList();
        }
        log.info("🔄 商品下架操作 | | 请求商品数: {} | listingIds: {}",
                listingIds.size(), listingIds);
        LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .in(KnetProduct::getListingId, listingIds)
                .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                .set(KnetProduct::getStatus, ProductStatus.OFF_SALE);
        try {
            int updatedCount = baseMapper.update(null, updateWrapper);
            log.info("✅ 商品下架操作完成 | 实际更新商品数: {} ", updatedCount);
        } catch (Exception e) {
            log.error("❌ 商品下架操作失败 | listingIds: {} | 错误信息: {}", listingIds, e.getMessage(), e);
            throw new ServiceException("商品下架失败");
        }
        return listingIds;
    }

    /**
     * 校验商品是否下架
     *
     * @param listingIds listingIds
     * @return 下架商品
     */
    @Override
    public List<OffSaleKnetProductResp.ProductDto> getOffSaleListingIds(List<String> listingIds) {
        if (CollUtil.isEmpty(listingIds)) {
            return Collections.emptyList();
        }
        List<String> offSaleListingIds = baseMapper.selectList(new LambdaQueryWrapper<KnetProduct>()
                        .in(KnetProduct::getListingId, listingIds)
                        .eq(KnetProduct::getStatus, ProductStatus.OFF_SALE)).stream()
                .map(KnetProduct::getListingId)
                .toList();
        return offSaleListingIds.stream()
                .map(listingId -> new OffSaleKnetProductResp.ProductDto(listingId, true, null))
                .toList();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public OffSaleKnetProductResp.ProductDto processKnetProductPrice(UpdatePriceKnetProductRequest.ProductDto productDto) {
        if (!KnetCurrencyCode.USD.equals(productDto.getCurrency())) {
            throw new ServiceException("价格单位只能是美元");
        }
        // 获取变更前的商品信息
        KnetProduct beforeProduct = this.getOne(
                new LambdaQueryWrapper<KnetProduct>()
                        .eq(KnetProduct::getListingId, productDto.getListingId())
                        .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
        );
        if (beforeProduct == null) {
            log.warn("❌ 商品价格更新失败 | 商品不存在或已下架 | listingId: {} | ",
                    productDto.getListingId());
            throw new ServiceException("更新商品价格失败，商品不存在或已下架,失败的listingId:" + productDto.getListingId());
        }
        log.info("💰 商品价格更新 |  | listingId: {} | sku: {} | spec: {} | 价格变更: {} -> {}",
                productDto.getListingId(), beforeProduct.getSku(),
                beforeProduct.getSpec(), beforeProduct.getPrice(), productDto.getPrice());

        LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(KnetProduct::getListingId, productDto.getListingId())
                .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                .set(KnetProduct::getPrice, productDto.getPrice());
        boolean updated = this.update(null, updateWrapper);
        if (!updated) {
            log.error("❌ 商品价格更新失败 | 数据已被其他操作修改 | listingId: {} |  | 尝试更新价格: {}",
                    productDto.getListingId(), productDto.getPrice());
            throw new ServiceException("更新商品价格失败，数据已经被其他操作修改,失败的listingId:" + productDto.getListingId());
        }
        log.info("✅ 商品价格更新成功 | listingId: {} | 价格变更: {} -> {} ",
                productDto.getListingId(), beforeProduct.getPrice(), productDto.getPrice());
        return new OffSaleKnetProductResp.ProductDto(productDto.getListingId(), true, productDto.getPrice());
    }

    @Override
    public List<QueryKnetProductResp> queryKnetProductForApi(QueryKnetProductRequest request) {
        List<String> listingIds = request.getProducts().stream()
                .map(QueryKnetProductRequest.ProductDto::getListingId)
                .distinct()
                .toList();
        if (CollUtil.isEmpty(listingIds)) {
            return Collections.emptyList();
        }
        log.info("对外接口: 查询商品listingId{}", listingIds);
        LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .select(KnetProduct::getListingId
                        , KnetProduct::getCreateTime
                        , KnetProduct::getUpdateTime
                        , KnetProduct::getOneId
                        , KnetProduct::getSku
                        , KnetProduct::getSpec
                        , KnetProduct::getPrice
                        , KnetProduct::getStatus
                )
                .in(KnetProduct::getListingId, listingIds);
        List<KnetProduct> knetProducts = baseMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(knetProducts)) {
            return Collections.emptyList();
        }
        return knetProducts.stream()
                .map(KnetProduct::mapToQueryKnetProductResp)
                .toList();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void updateKnetProductForSysSkuInfo(List<SysUpdateSysSkuEvents> updateEvents) {
        if (CollUtil.isEmpty(updateEvents)) {
            return;
        }
        updateEvents.forEach(event -> {
            LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .eq(KnetProduct::getSku, event.getSku())
                    .set(StrUtil.isNotBlank(event.getBrand()), KnetProduct::getBrand, event.getBrand())
                    .set(StrUtil.isNotBlank(event.getRemarks()), KnetProduct::getRemarks, event.getRemarks());
            this.update(null, updateWrapper);
            String redisSysSkuKey = String.format(KNET_SYS_SKU_CACHE_KEY_PREFIX, event.getSku());
            if (RedisCacheUtil.hasKey(redisSysSkuKey)) {
                RedisCacheUtil.del(redisSysSkuKey);
            }
        });
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void setProductModifyMark(List<String> skus, ProductMark productMark) {
        if (CollUtil.isEmpty(skus)) {
            return;
        }
        LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .in(KnetProduct::getSku, skus)
                .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                .set(null != productMark, KnetProduct::getMark, productMark);
        try {
            baseMapper.update(null, updateWrapper);
        } catch (Exception e) {
            throw new ServiceException("更新商品标识失败");
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @Override
    public void resetProductMarkToCommon(ProductMark productMark) {
        LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(KnetProduct::getMark, productMark)
                .set(null != productMark, KnetProduct::getMark, ProductMark.COMMON);
        try {
            baseMapper.update(null, updateWrapper);
        } catch (Exception e) {
            throw new ServiceException("更新商品标识失败");
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public int updateExistingProductsToOffSale(List<String> oneIds) {
        if (CollUtil.isEmpty(oneIds)) {
            return 0;
        }
        try {
            // 查询已存在且状态为ON_SALE的oneId列表
            LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                    .in(KnetProduct::getOneId, oneIds)
                    .eq(KnetProduct::getStatus, ProductStatus.ON_SALE);
            List<KnetProduct> existingProducts = baseMapper.selectList(queryWrapper);
            if (CollUtil.isEmpty(existingProducts)) {
                log.info("没有找到需要下架的商品");
                return 0;
            }
            log.info("找到 {} 个需要下架的商品", existingProducts.size());
            // 将这些商品的状态设置为OFF_SALE
            LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .in(KnetProduct::getOneId, oneIds)
                    .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                    .set(KnetProduct::getStatus, ProductStatus.OFF_SALE);
            int updatedCount = baseMapper.update(null, updateWrapper);
            log.info("成功下架 {} 个商品", updatedCount);
            return updatedCount;
        } catch (Exception e) {
            log.error("下架已存在商品失败: {}", e.getMessage(), e);
            throw new ServiceException("下架已存在商品失败");
        }
    }

    /**
     * 获取本地所有上架状态商品的oneId
     *
     * @return 本地上架状态商品的oneId集合
     */
    @Override
    public Set<String> getLocalOnSaleOneIds() {
        LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .select(KnetProduct::getOneId)
                .eq(KnetProduct::getStatus, ProductStatus.ON_SALE);
        List<KnetProduct> onSaleProducts = baseMapper.selectList(queryWrapper);
        return onSaleProducts.stream()
                .map(KnetProduct::getOneId)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());
    }

    @DistributedLock(key = "'inventory:legacy:lock:' + #request.hashCode()", expire = 30)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public boolean lockInventory(CheckAndLockInvRequest request) {
        if (CollUtil.isEmpty(request.getItems())) {
            log.warn("⚠️ 库存锁定 | 商品列表为空 ");
            return false;
        }

        log.info("🔒 库存锁定开始 | 商品种类数: {} | 总订单数量: {}",
                request.getItems().size(),
                request.getItems().stream().mapToInt(SubOrderItemResp::getCount).sum());
        request.getItems().forEach(item -> {
            // 将策略价格转换为原始价格进行库存匹配
            Long strategyPriceCents = PriceFormatUtil.formatYuanToCents(item.getPrice());
            Long originalPriceCents = pricingStrategyService.removePricingStrategy(strategyPriceCents);
            log.info("库存锁定价格转换: SKU={}, 策略价格={}美分, 原始价格={}美分",
                    item.getSku(), strategyPriceCents, originalPriceCents);
            LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                    .eq(KnetProduct::getSku, item.getSku())
                    .eq(KnetProduct::getPrice, originalPriceCents)
                    .eq(KnetProduct::getSpec, item.getSize())
                    .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                    .orderByDesc(KnetProduct::getCreateTime)
                    .last("LIMIT " + item.getCount());
            List<KnetProduct> productsToLock = baseMapper.selectList(queryWrapper);
            if (productsToLock.size() < item.getCount()) {
                log.error("❌ 库存锁定失败 | SKU: {} | 尺码: {} | 原始价格: {} | 请求数量: {} | 可用数量: {} | 原因: 库存不足",
                        item.getSku(), item.getSize(), originalPriceCents, item.getCount(), productsToLock.size());
                throw new ServiceException("商品 " + item.getSku() + " 尺码 " + item.getSize() + " 库存不足");
            }
            List<Long> idsToLock = productsToLock.stream().map(KnetProduct::getId).toList();
            try {
                // 执行锁定操作，确保只锁定ON_SALE状态的商品
                LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
                // 再次确认状态为ON_SALE
                updateWrapper
                        .in(KnetProduct::getId, idsToLock)
                        .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                        .set(KnetProduct::getStatus, ProductStatus.LOCKED);
                int updatedCount = baseMapper.update(null, updateWrapper);
                if (updatedCount != idsToLock.size()) {
                    log.error("商品锁定失败，期望锁定{}个，实际锁定{}个。可能原因：商品状态已变更或被其他实例锁定",
                            idsToLock.size(), updatedCount);
                    throw new ServiceException("商品 " + item.getSku() + " 尺码 " + item.getSize() + " 锁定失败，商品状态已变更");
                }
                log.info("✅ 库存锁定成功 | SKU: {} | 尺码: {} | 原始价格: {} | 锁定数量: {} | 商品ID: {}",
                        item.getSku(), item.getSize(), originalPriceCents, updatedCount, idsToLock);
            } catch (Exception e) {
                log.error("❌ 商品锁定失败 | SKU: {} | 尺码: {} | 原始价格: {} | 错误信息: {}",
                        item.getSku(), item.getSize(), originalPriceCents, e.getMessage(), e);
                throw new ServiceException("锁定库存失败");
            }
        });
        log.info("🔒 库存锁定完成 | 所有商品锁定成功 |");
        return true;
    }

    @Override
    public boolean checkInventory(CheckAndLockInvRequest request) {
        if (CollUtil.isEmpty(request.getItems())) {
            return false;
        }
        request.getItems().forEach(item -> {
            // 将策略价格转换为原始价格进行库存检查
            Long strategyPriceCents = PriceFormatUtil.formatYuanToCents(item.getPrice());
            Long originalPriceCents = pricingStrategyService.removePricingStrategy(strategyPriceCents);
            log.debug("库存检查价格转换: SKU={}, 策略价格={}美分, 原始价格={}美分",
                    item.getSku(), strategyPriceCents, originalPriceCents);
            LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                    .eq(KnetProduct::getSku, item.getSku())
                    .eq(KnetProduct::getPrice, originalPriceCents)
                    .eq(KnetProduct::getSpec, item.getSize())
                    .eq(KnetProduct::getStatus, ProductStatus.ON_SALE);
            List<KnetProduct> knetProducts = baseMapper.selectList(queryWrapper);
            if (CollUtil.isEmpty(knetProducts)) {
                log.error("商品: {}, 尺码: {}, 原始价格: {} 不存在", item.getSku(), item.getSize(), originalPriceCents);
                throw new ServiceException("商品 " + item.getSku() + " 尺码 " + item.getSize() + " 不存在");
            }
            if (knetProducts.size() < item.getCount()) {
                log.error("商品: {} 尺码: {} 原始价格: {} 库存不足", item.getSku(), item.getSize(), originalPriceCents);
                throw new ServiceException("商品 " + item.getSku() + " 尺码 " + item.getSize() + " 库存不足");
            }
        });
        return true;
    }

    @Override
    public List<QueryKnetProductResp> queryKnetProduct(InnerKnetProductRequest request) {
        LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(StrUtil.isNotBlank(request.getOneId()), KnetProduct::getOneId, request.getOneId())
                .eq(StrUtil.isNotBlank(request.getSku()), KnetProduct::getSku, request.getSku())
                .eq(StrUtil.isNotBlank(request.getSpec()), KnetProduct::getSpec, request.getSpec())
                .eq(BeanUtil.isNotEmpty(request.getStatus()), KnetProduct::getStatus, request.getStatus())
                .ne(StrUtil.isNotBlank(request.getAccount()), KnetProduct::getSource, request.getAccount());
        List<KnetProduct> knetProducts = baseMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(knetProducts)) {
            return Collections.emptyList();
        }
        return knetProducts.stream()
                .map(KnetProduct::mapToQueryKnetProductResp)
                .toList();
    }

    @Override
    public IPage<ProductPageQueryResp> queryProductsPage(ProductPageQueryRequest request) {
        return null;
    }

    @Override
    public List<KnetProduct> getKnetProductsByListingIds(List<String> listingIds) {
        if (CollUtil.isEmpty(listingIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(KnetProduct::getListingId, listingIds);
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 将策略价格转换为原始价格用于数据库查询
     * 前端传来的是策略价格（美元），需要转换为原始价格（美分）进行数据库筛选
     *
     * @param request 查询请求
     */
    private void convertStrategyPriceToOriginalPrice(ProductQueryRequest request) {
        if (StrUtil.isNotBlank(request.getMinPrice()) && StrUtil.isNotBlank(request.getMaxPrice())) {
            try {
                // 将策略价格（美元）转换为美分
                long minStrategyPriceCents = PriceFormatUtil.formatYuanToCents(request.getMinPrice());
                if (minStrategyPriceCents == 0) {
                    minStrategyPriceCents = 1100L;
                }
                long maxStrategyPriceCents = PriceFormatUtil.formatYuanToCents(request.getMaxPrice());
                // 将策略价格转换为原始价格
                long minOriginalPriceCents = pricingStrategyService.removePricingStrategy(minStrategyPriceCents);
                long maxOriginalPriceCents = pricingStrategyService.removePricingStrategy(maxStrategyPriceCents);
                // 将原始价格设置回请求对象（用于数据库查询）
                request.setMinPrice(String.valueOf(minOriginalPriceCents));
                request.setMaxPrice(String.valueOf(maxOriginalPriceCents));
                log.debug("价格区间转换: 策略价格[{}-{}]美元 -> 原始价格[{}-{}]美分",
                        request.getMinPrice(), request.getMaxPrice(),
                        minOriginalPriceCents, maxOriginalPriceCents);
            } catch (Exception e) {
                log.warn("价格区间转换失败，将忽略价格筛选条件: {}", e.getMessage());
                request.setMinPrice(null);
                request.setMaxPrice(null);
            }
        }
    }
}
