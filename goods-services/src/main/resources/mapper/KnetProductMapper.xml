<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.knet.goods.mapper.KnetProductMapper">

    <!--查询商品列表-按sku分组-->
    <select id="queryProductGroupBySku"
            parameterType="com.knet.goods.model.dto.req.ProductQueryRequest"
            resultType="com.knet.goods.model.dto.resp.ProductBySkuDtoResp">
        SELECT
        kp.sku,
        kp.remarks,
        kp.brand,
        kp.mark,
        MAX( kp.price ) AS maxPrice,
        MIN( kp.price ) AS minPrice,
        COUNT( * ) AS total
        FROM
        knet_product kp
        <where>
            kp.del_flag = 0
            AND kp.price > 0
            AND kp.`status` in ('ON_SALE')
            <!-- 品牌筛选：支持单选和多选 -->
            <choose>
                <!-- 多选品牌筛选 -->
                <when test="request.brands != null and !request.brands.isEmpty()">
                    AND kp.brand IN
                    <foreach collection="request.brands" item="brandItem" open="(" separator="," close=")">
                        #{brandItem}
                    </foreach>
                </when>
                <!-- 单选品牌筛选（向后兼容） -->
                <when test="request.brand != null and request.brand != ''">
                    AND kp.brand = #{request.brand}
                </when>
            </choose>
            <!-- 动态处理 skus 和 remarks 集合 -->
            <choose>
                <!-- 场景：skus 或 remarks 至少一个非空 -->
                <when test="(request.skus != null and !request.skus.isEmpty()) or (request.remarks != null and !request.remarks.isEmpty())">
                    AND (
                    <!-- 当 skus 非空时，添加 sku_indexed 条件 -->
                    <if test="request.skus != null and !request.skus.isEmpty()">
                        kp.sku_indexed IN
                        <foreach collection="request.skus" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <!-- 当 skus 和 remarks 均非空时，用 OR 连接 -->
                    <if test="request.skus != null and !request.skus.isEmpty() and request.remarks != null and !request.remarks.isEmpty()">
                        OR
                    </if>
                    <!-- 当 remarks 非空时，添加 remarks 条件 -->
                    <if test="request.remarks != null and !request.remarks.isEmpty()">
                        kp.remarks IN
                        <foreach collection="request.remarks" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    )
                </when>
            </choose>
            <if test="request.mark != null and request.mark.name() != ''">
                AND kp.mark = #{request.mark.name}
            </if>
            <if test="request.account != null and request.account != ''">
                AND kp.source !=#{request.account}
            </if>
            <!-- 尺码筛选：支持单选和多选 -->
            <choose>
                <!-- 多选尺码筛选 -->
                <when test="request.specs != null and !request.specs.isEmpty()">
                    AND kp.spec IN
                    <foreach collection="request.specs" item="specItem" open="(" separator="," close=")">
                        #{specItem}
                    </foreach>
                </when>
                <!-- 单选尺码筛选（向后兼容） -->
                <when test="request.spec != null and request.spec != ''">
                    AND kp.spec = #{request.spec}
                </when>
            </choose>
        </where>
        GROUP BY
        kp.sku
        <if test="request.minTotal != null and request.maxTotal != null or request.minPrice != null and request.maxPrice != null">
            HAVING 1=1
            <if test="request.minTotal != null and request.maxTotal != null">
                AND COUNT(*) BETWEEN #{request.minTotal} AND #{request.maxTotal}
            </if>
            <if test="request.minPrice != null and request.maxPrice != null">
                AND MIN(kp.price) &lt;= #{request.maxPrice} AND MAX(kp.price) &gt;= #{request.minPrice}
            </if>
        </if>
        <choose>
            <when test="request.sortBy != null">
                <choose>
                    <when test="request.sortBy.name() == 'UNIT_DESC'">
                        ORDER BY total DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'UNIT_ASC'">
                        ORDER BY total ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_DESC'">
                        ORDER BY maxPrice DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_ASC'">
                        ORDER BY maxPrice ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_DESC'">
                        ORDER BY sku DESC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_ASC'">
                        ORDER BY sku ASC
                    </when>
                </choose>
            </when>
        </choose>
        <!-- 计算分页偏移量 -->
        <bind name="offset" value="(request.pageNo - 1) * request.pageSize"/>
        LIMIT #{offset}, #{request.pageSize}
    </select>
    <!--分页查询总页数-->
    <select id="queryProductGroupBySkuCount"
            parameterType="com.knet.goods.model.dto.req.ProductQueryRequest"
            resultType="java.lang.Integer">
        SELECT COUNT( * ) AS total FROM(
        SELECT
        kp.sku,
        kp.remarks,
        kp.brand,
        kp.mark,
        MAX( kp.price ) AS maxPrice,
        MIN( kp.price ) AS minPrice,
        COUNT( * ) AS total
        FROM
        knet_product kp
        <where>
            kp.del_flag = 0
            AND kp.price > 0
            AND kp.`status` in ('ON_SALE')
            <if test="request.brand != null and request.brand != ''">
                AND kp.brand = #{request.brand}
            </if>
            <!-- 动态处理 skus 和 remarks 集合 -->
            <!-- 动态处理 skus 和 remarks 集合 -->
            <choose>
                <!-- 场景：skus 或 remarks 至少一个非空 -->
                <when test="(request.skus != null and !request.skus.isEmpty()) or (request.remarks != null and !request.remarks.isEmpty())">
                    AND (
                    <!-- 当 skus 非空时，添加 sku_indexed 条件 -->
                    <if test="request.skus != null and !request.skus.isEmpty()">
                        kp.sku_indexed IN
                        <foreach collection="request.skus" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <!-- 当 skus 和 remarks 均非空时，用 OR 连接 -->
                    <if test="request.skus != null and !request.skus.isEmpty() and request.remarks != null and !request.remarks.isEmpty()">
                        OR
                    </if>
                    <!-- 当 remarks 非空时，添加 remarks 条件 -->
                    <if test="request.remarks != null and !request.remarks.isEmpty()">
                        kp.remarks IN
                        <foreach collection="request.remarks" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    )
                </when>
            </choose>
            <if test="request.mark != null and request.mark.name() != ''">
                AND kp.mark = #{request.mark.name}
            </if>
            <if test="request.account != null and request.account != ''">
                AND kp.source !=#{request.account}
            </if>
            <if test="request.spec != null and request.spec != ''">
                AND kp.spec = #{request.spec}
            </if>
        </where>
        GROUP BY
        kp.sku
        <if test="request.minTotal != null and request.maxTotal != null or request.minPrice != null and request.maxPrice != null">
            HAVING 1=1
            <if test="request.minTotal != null and request.maxTotal != null">
                AND COUNT(*) BETWEEN #{request.minTotal} AND #{request.maxTotal}
            </if>
            <if test="request.minPrice != null and request.maxPrice != null">
                AND MIN(kp.price) &lt;= #{request.maxPrice} AND MAX(kp.price) &gt;= #{request.minPrice}
            </if>
        </if>
        <choose>
            <when test="request.sortBy != null">
                <choose>
                    <when test="request.sortBy.name() == 'UNIT_DESC'">
                        ORDER BY total DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'UNIT_ASC'">
                        ORDER BY total ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_DESC'">
                        ORDER BY maxPrice DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_ASC'">
                        ORDER BY maxPrice ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_DESC'">
                        ORDER BY sku DESC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_ASC'">
                        ORDER BY sku ASC
                    </when>
                </choose>
            </when>
        </choose>
        ) t
    </select>

    <!--查询当前sku下的商品详情信息-->
    <select id="queryProductDetails" resultType="com.knet.goods.model.dto.resp.ProductSkuSpecPriceDtoResp">
        SELECT
        kp.sku,
        kp.spec,
        kp.mark,
        MAX(kp.price ) AS maxPrice,
        MIN(kp.price ) AS minPrice,
        AVG(kp.price) AS avgPrice,
        SUM(kp.price) AS totalPrice,
        COUNT( * ) AS total
        FROM
        knet_product kp
        WHERE
        kp.del_flag = 0
        AND kp.price > 0
        AND kp.`status` in ('ON_SALE')
        <if test="request.sku != null and request.sku != ''">
            AND kp.sku = #{request.sku}
        </if>
        <if test="request.account != null and request.account != ''">
            AND kp.source !=#{request.account}
        </if>
        GROUP BY
        kp.spec
    </select>
    <!--查询当前sku下的商品详情信息-价格信息-->
    <select id="queryProductDetailsPriceInfo"
            resultType="com.knet.goods.model.dto.resp.SpecPriceDto">
        SELECT
        kp.spec,
        kp.price AS price,
        COUNT( * ) AS qty
        FROM
        knet_product kp
        WHERE
        kp.del_flag = 0
        AND kp.price > 0
        AND kp.`status` in ('ON_SALE')
        <if test="request.sku != null and request.sku != ''">
            AND kp.sku = #{request.sku}
        </if>
        <if test="request.account != null and request.account != ''">
            AND kp.source !=#{request.account}
        </if>
        GROUP BY
        kp.sku,
        kp.spec,
        kp.price
    </select>

    <!--批量插入-允许部分失败-->
    <insert id="insertIgnoreBatch">
        INSERT IGNORE INTO knet_product
        (one_id, sku, spec, warehouse, status, listing_id, price,creator,source,brand,remarks,mark)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.oneId}
            , #{item.sku}
            , #{item.spec}
            , #{item.warehouse}
            , #{item.status}
            , #{item.listingId}
            , #{item.price}
            , #{item.creator}
            , #{item.source}
            , #{item.brand}
            , #{item.remarks}
            , #{item.mark}
            )
        </foreach>
    </insert>

    <!--更新商品为下架状态-->
    <update id="updateKnetProductForOffSale">
        UPDATE knet_product
        SET `status` = 'OFF_SALE'
        WHERE
        listing_id IN
        <foreach collection="oneIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND sku = #{sku}
        AND `status` = 'ON_SALE'
        AND del_flag = 0
    </update>

    <!-- ==================== 分表临时表优化 ==================== -->

    <!--创建SKU临时表-->
    <update id="createTempSkuListTable">
        CREATE TEMPORARY TABLE IF NOT EXISTS temp_sku_list
        (
            sku_value VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
            PRIMARY KEY (sku_value)
        ) ENGINE = MEMORY
          DEFAULT CHARSET = utf8mb4
          COLLATE = utf8mb4_unicode_ci
    </update>

    <!--创建备注临时表-->
    <update id="createTempRemarkListTable">
        CREATE TEMPORARY TABLE IF NOT EXISTS temp_remark_list
        (
            remark_value VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
            PRIMARY KEY (remark_value)
        ) ENGINE = MEMORY
          DEFAULT CHARSET = utf8mb4
          COLLATE = utf8mb4_unicode_ci
    </update>

    <!--批量插入SKU到SKU临时表-->
    <insert id="insertSkusToSkuTable">
        INSERT IGNORE INTO temp_sku_list (sku_value) VALUES
        <foreach collection="skus" item="sku" separator=",">
            (#{sku})
        </foreach>
    </insert>

    <!--批量插入备注到备注临时表-->
    <insert id="insertRemarksToRemarkTable">
        INSERT IGNORE INTO temp_remark_list (remark_value) VALUES
        <foreach collection="remarks" item="remark" separator=",">
            (#{remark})
        </foreach>
    </insert>

    <!--删除SKU临时表-->
    <update id="dropTempSkuListTable">
        DROP TEMPORARY TABLE IF EXISTS temp_sku_list
    </update>

    <!--删除备注临时表-->
    <update id="dropTempRemarkListTable">
        DROP TEMPORARY TABLE IF EXISTS temp_remark_list
    </update>

    <!--查询商品列表-按sku分组-使用分表优化-->
    <select id="queryProductGroupBySkuWithSeparateTables"
            parameterType="com.knet.goods.model.dto.req.ProductQueryRequest"
            resultType="com.knet.goods.model.dto.resp.ProductBySkuDtoResp">
        SELECT
        kp.sku,
        kp.remarks,
        kp.brand,
        kp.mark,
        MAX( kp.price ) AS maxPrice,
        MIN( kp.price ) AS minPrice,
        COUNT( * ) AS total
        FROM knet_product kp
        WHERE
        kp.del_flag = 0
        AND kp.price > 0
        AND kp.`status` in ('ON_SALE')
        <!-- 品牌筛选：支持单选和多选 -->
        <choose>
            <!-- 多选品牌筛选 -->
            <when test="request.brands != null and !request.brands.isEmpty()">
                AND kp.brand IN
                <foreach collection="request.brands" item="brandItem" open="(" separator="," close=")">
                    #{brandItem}
                </foreach>
            </when>
            <!-- 单选品牌筛选（向后兼容） -->
            <when test="request.brand != null and request.brand != ''">
                AND kp.brand = #{request.brand}
            </when>
        </choose>
        <!-- 使用分表进行SKU和商品品名 查询 -->
        AND (
        EXISTS (SELECT 1 FROM temp_sku_list ts WHERE ts.sku_value = kp.sku_indexed)
        OR EXISTS (SELECT 1 FROM temp_remark_list tr WHERE tr.remark_value = kp.remarks)
        )
        <if test="request.mark != null and request.mark.name() != ''">
            AND kp.mark = #{request.mark.name}
        </if>
        <if test="request.account != null and request.account != ''">
            AND kp.source !=#{request.account}
        </if>
        <!-- 尺码筛选：支持单选和多选 -->
        <choose>
            <!-- 多选尺码筛选 -->
            <when test="request.specs != null and !request.specs.isEmpty()">
                AND kp.spec IN
                <foreach collection="request.specs" item="specItem" open="(" separator="," close=")">
                    #{specItem}
                </foreach>
            </when>
            <!-- 单选尺码筛选（向后兼容） -->
            <when test="request.spec != null and request.spec != ''">
                AND kp.spec = #{request.spec}
            </when>
        </choose>
        GROUP BY
        kp.sku
        <if test="request.minTotal != null and request.maxTotal != null or request.minPrice != null and request.maxPrice != null">
            HAVING 1=1
            <if test="request.minTotal != null and request.maxTotal != null">
                AND COUNT(*) BETWEEN #{request.minTotal} AND #{request.maxTotal}
            </if>
            <if test="request.minPrice != null and request.maxPrice != null">
                AND MIN(kp.price) &lt;= #{request.maxPrice} AND MAX(kp.price) &gt;= #{request.minPrice}
            </if>
        </if>
        <choose>
            <when test="request.sortBy != null">
                <choose>
                    <when test="request.sortBy.name() == 'UNIT_DESC'">
                        ORDER BY total DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'UNIT_ASC'">
                        ORDER BY total ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_DESC'">
                        ORDER BY maxPrice DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_ASC'">
                        ORDER BY maxPrice ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_DESC'">
                        ORDER BY sku DESC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_ASC'">
                        ORDER BY sku ASC
                    </when>
                </choose>
            </when>
        </choose>
        <!-- 计算分页偏移量 -->
        <bind name="offset" value="(request.pageNo - 1) * request.pageSize"/>
        LIMIT #{offset}, #{request.pageSize}
    </select>

    <!--分页查询总页数-使用分表优化-->
    <select id="queryProductGroupBySkuCountWithSeparateTables"
            parameterType="com.knet.goods.model.dto.req.ProductQueryRequest"
            resultType="java.lang.Integer">
        SELECT COUNT( * ) AS total FROM(
        SELECT
        kp.sku,
        kp.remarks,
        kp.brand,
        kp.mark,
        MAX( kp.price ) AS maxPrice,
        MIN( kp.price ) AS minPrice,
        COUNT( * ) AS total
        FROM
        knet_product kp
        <where>
            kp.del_flag = 0
            AND kp.price > 0
            AND kp.`status` in ('ON_SALE')
            <!-- 品牌筛选：支持单选和多选 -->
            <choose>
                <!-- 多选品牌筛选 -->
                <when test="request.brands != null and !request.brands.isEmpty()">
                    AND kp.brand IN
                    <foreach collection="request.brands" item="brandItem" open="(" separator="," close=")">
                        #{brandItem}
                    </foreach>
                </when>
                <!-- 单选品牌筛选（向后兼容） -->
                <when test="request.brand != null and request.brand != ''">
                    AND kp.brand = #{request.brand}
                </when>
            </choose>
            <!-- 使用分表进行SKU和商品品名 查询 -->
            AND (
            EXISTS (SELECT 1 FROM temp_sku_list ts WHERE ts.sku_value = kp.sku_indexed)
            OR EXISTS (SELECT 1 FROM temp_remark_list tr WHERE tr.remark_value = kp.remarks)
            )
            <if test="request.mark != null and request.mark.name() != ''">
                AND kp.mark = #{request.mark.name}
            </if>
            <if test="request.account != null and request.account != ''">
                AND kp.source !=#{request.account}
            </if>
            <!-- 尺码筛选：支持单选和多选 -->
            <choose>
                <!-- 多选尺码筛选 -->
                <when test="request.specs != null and !request.specs.isEmpty()">
                    AND kp.spec IN
                    <foreach collection="request.specs" item="specItem" open="(" separator="," close=")">
                        #{specItem}
                    </foreach>
                </when>
                <!-- 单选尺码筛选（向后兼容） -->
                <when test="request.spec != null and request.spec != ''">
                    AND kp.spec = #{request.spec}
                </when>
            </choose>
        </where>
        GROUP BY
        kp.sku
        <if test="request.minTotal != null and request.maxTotal != null or request.minPrice != null and request.maxPrice != null">
            HAVING 1=1
            <if test="request.minTotal != null and request.maxTotal != null">
                AND COUNT(*) BETWEEN #{request.minTotal} AND #{request.maxTotal}
            </if>
            <if test="request.minPrice != null and request.maxPrice != null">
                AND MIN(kp.price) &lt;= #{request.maxPrice} AND MAX(kp.price) &gt;= #{request.minPrice}
            </if>
        </if>
        <choose>
            <when test="request.sortBy != null">
                <choose>
                    <when test="request.sortBy.name() == 'UNIT_DESC'">
                        ORDER BY total DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'UNIT_ASC'">
                        ORDER BY total ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_DESC'">
                        ORDER BY maxPrice DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_ASC'">
                        ORDER BY maxPrice ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_DESC'">
                        ORDER BY sku DESC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_ASC'">
                        ORDER BY sku ASC
                    </when>
                </choose>
            </when>
        </choose>
        ) t
    </select>

    <!-- 创建价格聚合表 -->
    <update id="createPriceAggregationTable">
        CREATE TABLE IF NOT EXISTS `sys_price_aggregation`
        (
            `sku`         VARCHAR(50)     NOT NULL,
            `spec`        VARCHAR(50)     NOT NULL DEFAULT '',
            `min_price`   DECIMAL(10, 2)  NOT NULL,
            `max_price`   DECIMAL(10, 2)  NOT NULL,
            `version`     BIGINT UNSIGNED NOT NULL DEFAULT 0,
            `create_time` TIMESTAMP                DEFAULT CURRENT_TIMESTAMP,
            `update_time` TIMESTAMP                DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY `uk_sku_spec` (`sku`, `spec`),
            INDEX `idx_sku` (`sku`),
            INDEX `idx_create_time` (`create_time`)
        ) ENGINE = InnoDB
          DEFAULT CHARSET = utf8mb4 COMMENT ='价格聚合表';
    </update>

    <!-- 查询价格聚合数据 -->
    <select id="selectPriceAggregation" resultType="com.knet.goods.model.dto.resp.PriceAggregationResp">
        SELECT sku, spec, min_price as minPrice, max_price as maxPrice, version, create_time as createTime
        FROM sys_price_aggregation
        WHERE sku = #{sku}
        <if test="spec != null and spec != ''">
            AND spec = #{spec}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 批量查询价格聚合数据 -->
    <select id="batchSelectPriceAggregation" resultType="com.knet.goods.model.dto.resp.PriceAggregationResp">
        SELECT sku, spec, min_price as minPrice, max_price as maxPrice, version, create_time as createTime
        FROM sys_price_aggregation
        WHERE (sku, spec) IN
        <foreach collection="skuSpecList" item="item" open="(" close=")" separator=",">
            (#{item.sku}, #{item.spec})
        </foreach>
        ORDER BY sku, spec
    </select>

    <!-- 插入或更新价格聚合数据 -->
    <insert id="insertOrUpdatePriceAggregation">
        INSERT INTO sys_price_aggregation (sku, spec, min_price, max_price, version)
        VALUES (#{sku}, #{spec}, #{minPrice}, #{maxPrice}, 1)
        ON DUPLICATE KEY UPDATE min_price   = #{minPrice},
                                max_price   = #{maxPrice},
                                version     = version + 1,
                                update_time = CURRENT_TIMESTAMP
    </insert>

    <!-- 乐观锁更新价格聚合数据 -->
    <update id="updatePriceAggregationWithVersion">
        UPDATE sys_price_aggregation
        SET min_price   = #{minPrice},
            max_price   = #{maxPrice},
            version     = version + 1,
            update_time = CURRENT_TIMESTAMP
        WHERE sku = #{sku}
          AND spec = #{spec}
          AND version = #{version}
    </update>

    <!-- 分页查询商品列表 -->
    <select id="queryProductsPage"
            parameterType="com.knet.goods.model.dto.req.ProductPageQueryRequest"
            resultType="com.knet.goods.model.dto.resp.ProductPageQueryResp">
        SELECT
        kp.id,
        kp.listing_id AS listingId,
        kp.one_id AS oneId,
        kp.sku,
        kp.spec,
        kp.brand,
        kp.remarks,
        ROUND(kp.price / 100, 2) AS price,
        kp.stock,
        kp.warehouse,
        kp.status,
        kp.mark,
        kp.creator,
        kp.source,
        kp.create_time AS createTime,
        kp.update_time AS updateTime
        FROM knet_product kp
        <where>
            kp.del_flag = 0
            <if test="request.status != null">
                AND kp.status = #{request.status}
            </if>
            <if test="request.oneId != null and request.oneId != ''">
                AND kp.one_id = #{request.oneId}
            </if>
            <if test="request.listingId != null and request.listingId != ''">
                AND kp.listing_id = #{request.listingId}
            </if>
            <if test="request.oneIds != null and request.oneIds.size() > 0">
                AND kp.one_id IN
                <foreach collection="request.oneIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.listingIds != null and request.listingIds.size() > 0">
                AND kp.listing_id IN
                <foreach collection="request.listingIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.sku != null and request.sku != ''">
                AND kp.sku LIKE CONCAT('%', #{request.sku}, '%')
            </if>
            <if test="request.brand != null and request.brand != ''">
                AND kp.brand = #{request.brand}
            </if>
            <if test="request.remarks != null and request.remarks != ''">
                AND kp.remarks LIKE CONCAT('%', #{request.remarks}, '%')
            </if>
            <if test="request.spec != null and request.spec != ''">
                AND kp.spec = #{request.spec}
            </if>
            <if test="request.warehouse != null and request.warehouse != ''">
                AND kp.warehouse = #{request.warehouse}
            </if>
            <if test="request.source != null and request.source != ''">
                AND kp.source = #{request.source}
            </if>
            <if test="request.creator != null and request.creator != ''">
                AND kp.creator = #{request.creator}
            </if>
        </where>
        ORDER BY kp.update_time DESC
        `LIMIT` #{request.queryStartPage}, #{request.pageSize}
    </select>

    <!-- 分页查询商品总数 -->
    <select id="queryProductsPageCount"
            parameterType="com.knet.goods.model.dto.req.ProductPageQueryRequest"
            resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM knet_product kp
        <where>
            kp.del_flag = 0
            <if test="request.status != null">
                AND kp.status = #{request.status}
            </if>
            <if test="request.oneId != null and request.oneId != ''">
                AND kp.one_id = #{request.oneId}
            </if>
            <if test="request.listingId != null and request.listingId != ''">
                AND kp.listing_id = #{request.listingId}
            </if>
            <if test="request.oneIds != null and request.oneIds.size() > 0">
                AND kp.one_id IN
                <foreach collection="request.oneIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.listingIds != null and request.listingIds.size() > 0">
                AND kp.listing_id IN
                <foreach collection="request.listingIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.sku != null and request.sku != ''">
                AND kp.sku LIKE CONCAT('%', #{request.sku}, '%')
            </if>
            <if test="request.brand != null and request.brand != ''">
                AND kp.brand = #{request.brand}
            </if>
            <if test="request.remarks != null and request.remarks != ''">
                AND kp.remarks LIKE CONCAT('%', #{request.remarks}, '%')
            </if>
            <if test="request.spec != null and request.spec != ''">
                AND kp.spec = #{request.spec}
            </if>
            <if test="request.warehouse != null and request.warehouse != ''">
                AND kp.warehouse = #{request.warehouse}
            </if>
            <if test="request.source != null and request.source != ''">
                AND kp.source = #{request.source}
            </if>
            <if test="request.creator != null and request.creator != ''">
                AND kp.creator = #{request.creator}
            </if>
        </where>
    </select>

</mapper>
