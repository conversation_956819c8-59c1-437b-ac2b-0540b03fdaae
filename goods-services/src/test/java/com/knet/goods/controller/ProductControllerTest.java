package com.knet.goods.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.knet.goods.model.dto.req.ProductQueryRequest;
import com.knet.goods.model.dto.resp.ProductBySkuDtoResp;
import com.knet.goods.service.IKnetProductService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * <AUTHOR>
 * @date 2025/9/1
 * @description: 商品控制器测试类 - 测试多选筛选和限流功能
 */
@WebMvcTest(ProductController.class)
public class ProductControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private IKnetProductService iKnetProductService;

    @Autowired
    private ObjectMapper objectMapper;

    private IPage<ProductBySkuDtoResp> mockPage;

    @BeforeEach
    void setUp() {
        // 模拟分页结果
        mockPage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>();
        when(iKnetProductService.queryProductGroupBySku(any(ProductQueryRequest.class)))
                .thenReturn(mockPage);
    }

    @Test
    void testQueryProductGroupBySku_WithMultipleBrands() throws Exception {
        // 测试多选品牌筛选
        ProductQueryRequest request = ProductQueryRequest.builder()
                .pageNo(1)
                .pageSize(10)
                .brands(Arrays.asList("Nike", "Adidas", "Jordan"))
                .account("test_user")
                .build();

        mockMvc.perform(post("/product/list")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"));
    }

    @Test
    void testQueryProductGroupBySku_WithMultipleSpecs() throws Exception {
        // 测试多选尺码筛选
        ProductQueryRequest request = ProductQueryRequest.builder()
                .pageNo(1)
                .pageSize(10)
                .specs(Arrays.asList("US 9", "US 10", "US 11"))
                .account("test_user")
                .build();

        mockMvc.perform(post("/product/list")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"));
    }

    @Test
    void testQueryProductGroupBySku_WithBothMultipleBrandsAndSpecs() throws Exception {
        // 测试同时使用多选品牌和尺码筛选
        ProductQueryRequest request = ProductQueryRequest.builder()
                .pageNo(1)
                .pageSize(10)
                .brands(Arrays.asList("Nike", "Jordan"))
                .specs(Arrays.asList("US 9", "US 10"))
                .account("test_user")
                .build();

        mockMvc.perform(post("/product/list")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"));
    }

    @Test
    void testQueryProductGroupBySku_BackwardCompatibility() throws Exception {
        // 测试向后兼容性 - 单选品牌和尺码
        ProductQueryRequest request = ProductQueryRequest.builder()
                .pageNo(1)
                .pageSize(10)
                .brand("Nike")
                .spec("US 10")
                .account("test_user")
                .build();

        mockMvc.perform(post("/product/list")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"));
    }

    @Test
    void testQueryProductGroupBySku_WithAllFilters() throws Exception {
        // 测试所有筛选条件组合
        ProductQueryRequest request = ProductQueryRequest.builder()
                .pageNo(1)
                .pageSize(10)
                .brands(Arrays.asList("Nike", "Adidas"))
                .specs(Arrays.asList("US 9", "US 10"))
                .minTotal(5)
                .maxTotal(100)
                .minPrice("50.00")
                .maxPrice("200.00")
                .account("test_user")
                .build();

        mockMvc.perform(post("/product/list")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"));
    }

    @Test
    void testRateLimiter_NormalRequest() throws Exception {
        // 测试正常请求不会被限流
        ProductQueryRequest request = ProductQueryRequest.builder()
                .pageNo(1)
                .pageSize(10)
                .account("test_user")
                .build();

        // 发送正常请求
        mockMvc.perform(post("/product/list")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    void testRateLimiter_DifferentUsers() throws Exception {
        // 测试不同用户的限流是独立的
        ProductQueryRequest request1 = ProductQueryRequest.builder()
                .pageNo(1)
                .pageSize(10)
                .account("user1")
                .build();

        ProductQueryRequest request2 = ProductQueryRequest.builder()
                .pageNo(1)
                .pageSize(10)
                .account("user2")
                .build();

        // 用户1的请求
        mockMvc.perform(post("/product/list")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request1)))
                .andExpect(status().isOk());

        // 用户2的请求应该也能正常通过
        mockMvc.perform(post("/product/list")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request2)))
                .andExpect(status().isOk());
    }

    @Test
    void testEmptyFilters() throws Exception {
        // 测试空筛选条件
        ProductQueryRequest request = ProductQueryRequest.builder()
                .pageNo(1)
                .pageSize(10)
                .brands(Arrays.asList())  // 空列表
                .specs(Arrays.asList())   // 空列表
                .account("test_user")
                .build();

        mockMvc.perform(post("/product/list")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    void testNullFilters() throws Exception {
        // 测试null筛选条件
        ProductQueryRequest request = ProductQueryRequest.builder()
                .pageNo(1)
                .pageSize(10)
                .brands(null)
                .specs(null)
                .account("test_user")
                .build();

        mockMvc.perform(post("/product/list")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
}
